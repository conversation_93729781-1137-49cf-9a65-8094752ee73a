@php
$categories = app('Webkul\\Category\\Repositories\\CategoryRepository')
->getModel()
->where('status', 1)
->where(function($q) {
$q->where('parent_id', 1)
->orWhere(function($query) {
$query->whereNull('parent_id')
->where('id', '!=', 1);
});
})
->orderBy('position')
->get();

$categories_all = app('Webkul\\Category\\Repositories\\CategoryRepository')
->findWhere(['parent_id' => 1]);

// Lấy brand đầu tiên
$firstBrand = \App\Brand::first();

// Lấy links từ footer để gán vào header
$channel = core()->getCurrentChannel();
$footerLinks = app('Webkul\\Theme\\Repositories\\ThemeCustomizationRepository')
->findWhere([
'channel_id' => $channel->id,
'theme_code' => 'medical',
'type' => 'footer_links',
'status' => 1
]);

$zaloLink = 'https://zalo.me/pc';
$facebookLink = 'https://www.facebook.com/';
$guideLink = '/guide'; // Fallback mặc định
$aboutUsLink = '/page/about-us'; // Fallback mặc định

if (count($footerLinks)) {
$translation = $footerLinks[0]->translations->first();
if ($translation) {
$footerOptions = is_array($translation->options)
? $translation->options
: json_decode($translation->options, true);

// Tìm links từ tất cả columns
$allLinks = [];
foreach (['column_1', 'column_2', 'column_3'] as $column) {
if (!empty($footerOptions[$column])) {
$allLinks = array_merge($allLinks, $footerOptions[$column]);
}
}

foreach ($allLinks as $link) {
$title = strtolower($link['title'] ?? '');
$url = $link['url'] ?? '#';

// Tìm Zalo và Facebook từ column_3
if (stripos($title, 'zalo') !== false || stripos($url, 'zalo') !== false) {
$zaloLink = $url;
} elseif (stripos($title, 'facebook') !== false || stripos($url, 'facebook') !== false) {
$facebookLink = $url;
}
// Tìm Hướng dẫn đặt hàng
elseif (stripos($title, 'hướng dẫn') !== false || stripos($title, 'đặt hàng') !== false) {
$guideLink = $url;
}
// Tìm Về chúng tôi / Giới thiệu công ty
elseif (stripos($title, 'về chúng tôi') !== false || stripos($title, 'giới thiệu') !== false || stripos($title, 'công ty') !== false) {
$aboutUsLink = $url;
}
}
}
}
@endphp

<header>
    <!-- Top bar -->
    <div class="header-top">
        <div class="header-top-row">
            <div class="link-section">
                <div class="header-text">Liên kết:</div>
                <a href="https://zalo.me/pc" target="_blank" rel="noopener noreferrer">
                    <img class="w-6 h-6" src="/images/zalo.png" alt="Zalo" style="cursor: pointer;" />
                </a>
                <a href="https://www.facebook.com/" target="_blank" rel="noopener noreferrer">
                    <img style="border-radius: 100%; cursor: pointer;" class="w-6 h-6" src="/images/facebook.svg" alt="Facebook" />
                </a>
            </div>
            <div class="hotline-section">
                <div class="header-text">Hotline: 0966.125.028 | 0836.460.822</div>
            </div>
        </div>
        <div class="nav-section">
            <div style="cursor: pointer;" onclick="window.location.href='/guide'" class="header-text">Hướng dẫn đặt hàng</div>
            <div onclick="window.location.href='/page/about-us'" class="header-text" style="cursor: pointer;">Về chúng tôi</div>
            <div onclick="window.location.href='/news'" class="header-text" style="cursor: pointer;">Tin tức</div>
        </div>
    </div>

    <!-- Main header -->
    <div class="main-header">
        <div onclick="window.location.href='/home'" style="cursor: pointer;" class="logo-container-header">
            <img class="logo-image" onclick="window.location.href='/home'" src="/images/logo_full.png" alt="Logo Phan Anh" class="suggestion-logo">
        </div>
        <div class="search-container p-[4px_4px_4px_16px] p cursor-auto h-fit">
            <form id="searchForm" action="/product" method="GET" class="w-full h-full">
                <div class="flex gap-[10px] w-full items-center">
                    <input type="text" name="search" placeholder="Nhập từ khóa bạn muốn tìm kiếm" class="search-input caret-[#F06F22] focus:outline-none focus:border-none focus:!shadow-none h-[36px] max-[480px]:h-[30px] max-[360px]:h-[28px] flex-1" id="searchInput" autocomplete="off">
                    <button type="submit" class="search-button">
                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                            <circle cx="9.58333" cy="9.58333" r="7.08333" stroke="white" stroke-width="1.5" />
                            <path d="M15 15L17.5 17.5" stroke="white" stroke-width="1.5" />
                        </svg>
                    </button>
                </div>
            </form>
            <div class="search-suggestions" id="searchSuggestions" style="display: none;">
                <div class="suggestions-header">
                    @if($firstBrand)
                        <span class="flex-1">{{ $firstBrand->brand_name }}</span>
                        @if($firstBrand->brand_image)
                            <img src="{{ asset('storage/' . $firstBrand->brand_image) }}" alt="{{ $firstBrand->brand_name }}" class="suggestion-logo">
                        @else
                            <img src="/images/logo_full.png" alt="{{ $firstBrand->brand_name }}" class="suggestion-logo">
                        @endif
                    @else
                        <span class="flex-1">DPA - thuocphananh.com / thuocphananh.vn</span>
                        <img src="/images/logo_full.png" alt="Logo Phan Anh" class="suggestion-logo">
                    @endif
                </div>

                <div class="my-[15px] max-lg:my-[10px] w-full border-t-[1px] border-[#E0E0E0]" id="historySeparator"> </div>

                <div class="suggestions-row" id="historySection">
                    <div>Lịch sử tìm kiếm</div>
                    <div class="clear-all" onclick="clearSearchHistory()">Xóa tất cả</div>
                </div>
                <div id="searchHistoryList" class="flex flex-col gap-[2px]">
                    <!-- Lịch sử tìm kiếm sẽ được load động -->
                </div>


                <div class="my-[15px] max-lg:my-[10px] w-full border-t-[1px] border-[#E0E0E0]"> </div>
                
                <div class="suggestions-title">Tìm kiếm phổ biến</div>
                <div class="suggestions-tags mt-2" id="popularSearchTags">
                    <!-- Tags phổ biến sẽ được load động -->
                </div>
            </div>
            <div id="input-suggestion" class="input-suggestion-container" style="display: none;">
                <p style="font-weight: bold;margin-bottom: 8px;font-size: 14px;">Danh mục</p>
                <!-- Các mục gợi ý danh mục -->
                <div class="category-suggestions" id="categorySuggestions">
                    <!-- Categories sẽ được load động -->
                </div>

                <!-- Đường phân cách -->
                <div class="suggestion-divider" id="suggestionDivider"></div>

                <!-- Các sản phẩm gợi ý -->
                <div class="product-suggestions" id="productSuggestions">
                    <!-- Products sẽ được load động -->
                </div>
            </div>
        </div>
        <div class="auth-section flex gap-2">
            @if(Auth::guard('customer')->check())
            <div class="user-dropdown-wrapper" style="position: relative; display: inline-block;">
                <button id="userDropdownBtn" type="button" style="background: none; border: none; cursor: pointer;">
                    <i class="fa-solid fa-user fa-2x" style="color: #fffafa;"></i>
                </button>
                <!-- Cầu nối giữa icon và menu -->
                <div style="position: absolute; height: 15px; width: 100%; top: 100%; left: 0;"></div>
                <div id="userDropdownMenu" class="user-dropdown-menu" style="display: none; top: 55px;">
                    <div class="user-dropdown-divider"></div>
                    <a href="/profile/customer" class="user-dropdown-item">Thông tin cá nhân</a>
                    <a href="/profile/cost" class="user-dropdown-item">Báo giá của tôi</a>
                    <a href="/profile/orders" class="user-dropdown-item">Đơn hàng của tôi</a>
                    <form action="{{ route('logout') }}" method="POST" style="margin:0;">
                        @csrf
                        <button type="submit" class="user-dropdown-item logout-btn">Đăng xuất</button>
                    </form>
                </div>
            </div>
            <div class="quote-button aspect-square relative size-10 p-0" onclick="window.location.href='/quote'">
                <span  class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" >
                    <i class="fa-solid fa-file-invoice-dollar"style="color: #f6f7f9;"></i>
                </span>
                <div class="quote-count" id="header-quote-count">
                    @php
                    $quoteItemsCount = 0;
                    if (Auth::guard('customer')->check()) {
                    $quote = \App\Quote::where('customer_id', Auth::guard('customer')->id())
                    ->where('status', 'pending')
                    ->orderBy('created_at', 'desc')
                    ->first();
                    if ($quote) {
                    $quoteItemsCount = $quote->items()->count();
                    }
                    }
                    @endphp
                    {{ $quoteItemsCount }}
                </div>
            </div>
            <div class="cart-button aspect-square relative size-10 p-0" onclick="window.location.href='/cart'">
                <span>
                    <img   class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 size-5" src="/images/buy.svg" alt="cart"/>
                </span>
                <div class="cart-count">
                    @php
                    $cartItemsCount = 0;
                    if (Auth::guard('customer')->check()) {
                    $cart = \Webkul\Checkout\Facades\Cart::getCart();
                    if ($cart) {
                    $cartItemsCount = $cart->items->count();
                    }
                    }
                    @endphp
                    {{ $cartItemsCount }}
                </div>
            </div>
            @else
            <button class="register-button" onclick="window.location.href='/signup'">Đăng ký</button>
            <button class="login-button" onclick="window.location.href='/signin'">Đăng nhập</button>
            @endif
            

            <!-- Mobile menu button (hiện khi màn hình < 768px) -->
            <button class="mobile-menu-btn" onclick="toggleMobileMenu()">
                <i class="fas fa-bars" style="color: #f6f7f9;"></i>
            </button>
        </div>
    </div>

    <!-- Navigation -->
    <nav>
        <div class="container-nav">
            <!-- Fixed category button -->
            <div class="nav-category-fixed">
                <li class="dropdown">
                    <a class="dropdown-toggle" style="background-color: #fff0e6;margin-right: 40px;">
                        <i class="bi bi-grid" style="color: #FF6B00;"></i>
                        <span style="color: #FF6B00;" class="menu-icon">Danh mục</span><i class="fa-solid fa-chevron-down" style="color: #ff6b00;"></i>
                    </a>
                    <div class="dropdown-menu mega-menu">
                        <div class="mega-menu-container">
                            <div class="mega-menu-left">
                                @foreach($categories_all as $category)
                                @php
                                    $categoryUrl = '';
                                    if (!empty($category->additional)) {
                                        $additional = json_decode($category->additional, true);
                                        $categoryUrl = $additional['category_url'] ?? '';
                                    }
                                @endphp
                                <div class="mega-menu-item {{ $loop->first ? 'active' : '' }}"
                                     data-category="{{ $category->slug }}"
                                     @if(!empty($categoryUrl))
                                         onclick="window.location.href='{{ $categoryUrl }}'"
                                         style="cursor: pointer;"
                                     @endif>
                                    <img class="mega-category_image" src="{{ asset('storage/' . $category->logo_path) }}">
                                    <span class="mega-menu-parent-title">{{ $category->name }}</span>
                                </div>
                                @endforeach
                            </div>

                            <div class="mega-menu-right">
                                @foreach($categories_all as $category)
                                @php
                                $children = $category->children()->where('status', 1)->get();
                                @endphp

                                @if($children->count())
                                <div class="mega-menu-content{{ $loop->first ? ' active' : '' }}" id="{{ $category->slug }}">
                                    <div class="mega-menu-grid">
                                        @foreach($children as $index => $child)
                                            @php
                                                // Lấy 1 product_id bất kỳ của category_id = $child->id
                                                $categoryData = DB::table('categories')
                                                    ->where('id', $child->id)
                                                    ->limit(1)
                                                    ->first();

                                                $imagePath = '/images/product.png'; // Mặc định nếu không có ảnh

                                                if($categoryData) {
                                                    $imagePath = $categoryData->logo_path; // Giả sử cột ảnh tên là 'path'
                                                }
                                            @endphp

                                            <div class="category-item {{ $index >= 3 ? 'hidden-category' : '' }}" onclick="window.location.href='/product?category={{ $child->slug }}'">
                                                <img class="category_image" src="{{ asset('storage/' . $imagePath) }}">
                                                <a title="{{ $child->name }}" href="/product?category={{ $child->slug }}" class="mega-menu-subitem">
                                                    <div>{{ $child->name }}</div>
                                                </a>
                                            </div>
                                        @endforeach

                                        @if($children->count() > 3)
                                        <div class="category-item show-all-btn-{{ $category->id }}" onclick="showAllCategories({{ $category->id }}, '{{ (string) $category->slug }}')">
                                            <img style="padding: 15% 15% 15% 15%;" class="category_image" src="/images/all.png">
                                            <a title="Xem tất cả" href="javascript:void(0);" class="mega-menu-subitem">
                                                <div>Xem tất cả</div>
                                            </a>
                                        </div>
                                        @endif
                                    </div>
                                </div>
                                @endif
                                @endforeach

                            </div>
                        </div>
                    </div>
                </li>
            </div>

            <!-- Navigation scroll buttons -->
            <button class="nav-scroll-btn nav-scroll-left" onclick="scrollNavigation('left')" style="display: none;">
                <i class="fas fa-chevron-left"></i>
            </button>

            <!-- Scrollable menu items -->
            <ul id="navigation-menu">
                @foreach($categories as $category)
                <li class="dropdown">
                    <a href="{{ $category->slug == 'bao-gia' ? route('quick_order') : ($category->slug == 'quickorder' ? route('quick_order') : route('our_product', ['category' => $category->slug])) }}" class="dropdown-toggle">
                        {{ $category->name }}
                    </a>
                    @php
                    $children = $category->children()->where('status', 1)->get();
                    @endphp
                    @if($children->count())
                    <div class="dropdown-menu mega-menu">
                        <div class="mega-menu-container">
                            <div class="mega-menu-left">
                                @foreach($children as $child)
                                <div class="mega-menu-item" data-category="{{ $child->slug }}">
                                    <a href="#">
                                        {{ $child->name }}
                                    </a>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @endif
                </li>
                @endforeach
            </ul>

            <!-- Right scroll button -->
            <button class="nav-scroll-btn nav-scroll-right" onclick="scrollNavigation('right')" style="display: none;">
                <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </nav>

    <!-- Mobile Navigation Menu (slide từ bên phải) -->
    <div class="mobile-nav-overlay" id="mobileNavOverlay" onclick="closeMobileMenu()"></div>
    <div class="mobile-nav-menu" id="mobileNavMenu">
        <div class="mobile-nav-header">
            <h3>Danh mục sản phẩm</h3>
            <button class="mobile-nav-close" onclick="closeMobileMenu()">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="mobile-nav-content">




            <!-- Danh mục sản phẩm -->
            <div class="mobile-category-section">
                <div class="mobile-nav-item mobile-parent-category" onclick="toggleMobileCategory()">
                    <span>Danh mục sản phẩm</span>
                    <i class="fas fa-chevron-down mobile-category-arrow"></i>
                </div>

                <div class="mobile-category-submenu" id="mobileCategorySubmenu">
                    @foreach($categories_all as $category)
                    @php
                        $children = $category->children()->where('status', 1)->get();
                    @endphp

                    @if($children->count() > 0)
                        <!-- Category có con -->
                        <div class="mobile-category-item mobile-parent-category" onclick="toggleMobileSubcategory('{{ $category->slug }}')">
                            <img src="{{ asset('storage/' . $category->logo_path) }}" alt="{{ $category->name }}">
                            <span>{{ $category->name }}</span>
                            <i class="fas fa-chevron-down mobile-subcategory-arrow" id="arrow-{{ $category->slug }}"></i>
                        </div>

                        <!-- Submenu của category -->
                        <div class="mobile-subcategory-menu" id="submenu-{{ $category->slug }}">
                            <!-- Các danh mục con -->
                            @foreach($children->take(15) as $child)
                            <div class="mobile-subcategory-item" onclick="window.location.href='/product?category={{ $child->slug }}'">
                                @php
                                    $childData = DB::table('categories')
                                        ->where('id', $child->id)
                                        ->limit(1)
                                        ->first();

                                    $childImagePath = $childData && $childData->logo_path
                                        ? $childData->logo_path
                                        : '/images/product.png';
                                @endphp
                                <img src="{{ asset('storage/' . $childImagePath) }}" alt="{{ $child->name }}">
                                <span>{{ $child->name }}</span>
                            </div>
                            @endforeach

                            @if($children->count() > 15)
                            <div class="mobile-subcategory-item mobile-see-all" onclick="window.location.href='/product?category={{ $category->slug }}'">
                                <i class="fas fa-chevron-right"></i>
                                <span>Xem tất cả</span>
                            </div>
                            @endif
                        </div>
                    @endif
                    @endforeach
                </div>
            </div>

            <!-- Menu navigation chính (giống desktop) -->
            @foreach($categories as $category)
            @php
                $children = $category->children()->where('status', 1)->get();
            @endphp

            @if($children->count() > 0)
                <!-- Category có con -->
                <div class="mobile-nav-section">
                    <div class="mobile-nav-item" onclick="window.location.href='{{ $category->slug == 'bao-gia' ? route('quick_order') : route('our_product', ['category' => $category->slug]) }}'">
                        <span>{{ $category->name }}</span>
                        <i class="fas fa-chevron-right" id="arrow-nav-{{ $category->slug }}"></i>
                    </div>
                </div>
            @else
                <!-- Category không có con -->
                <div class="mobile-nav-section">
                    <div class="mobile-nav-item" onclick="window.location.href='{{ $category->slug == 'bao-gia' ? route('quick_order') : route('our_product', ['category' => $category->slug]) }}'">
                        <span>{{ $category->name }}</span>
                        <i class="fas fa-chevron-right"></i>
                    </div>
                </div>
            @endif
            @endforeach

            <!-- Links khác -->
            <div class="mobile-nav-section">
                <div class="mobile-nav-item" onclick="window.location.href='{{ $guideLink }}'">
                    <span>Hướng dẫn đặt hàng</span>
                    <i class="fas fa-chevron-right"></i>
                </div>
                <div class="mobile-nav-item" onclick="window.location.href='{{ $aboutUsLink }}'">
                    <span>Về chúng tôi</span>
                    <i class="fas fa-chevron-right"></i>
                </div>
                <div class="mobile-nav-item" onclick="window.location.href='/news'">
                    <span>Tin tức</span>
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>
        </div>
    </div>
</header>

<script>
// Mobile Navigation Functions
function toggleMobileMenu() {
    const overlay = document.getElementById('mobileNavOverlay');
    const menu = document.getElementById('mobileNavMenu');

    overlay.classList.add('active');
    menu.classList.add('active');

    // Store current scroll position
    window.mobileMenuScrollY = window.scrollY;

    // Prevent body scroll when menu is open
    document.body.classList.add('mobile-menu-open');
    document.body.style.top = `-${window.mobileMenuScrollY}px`;
}

function closeMobileMenu() {
    const overlay = document.getElementById('mobileNavOverlay');
    const menu = document.getElementById('mobileNavMenu');

    overlay.classList.remove('active');
    menu.classList.remove('active');

    // Restore body scroll and position
    document.body.classList.remove('mobile-menu-open');
    document.body.style.top = '';

    // Restore scroll position
    if (window.mobileMenuScrollY !== undefined) {
        window.scrollTo(0, window.mobileMenuScrollY);
        window.mobileMenuScrollY = undefined;
    }

    // Close main category submenu if open
    const mainSubmenu = document.getElementById('mobileCategorySubmenu');
    const mainArrow = document.querySelector('.mobile-category-section .mobile-category-arrow');
    if (mainSubmenu && mainSubmenu.classList.contains('active')) {
        mainSubmenu.classList.remove('active');
        if (mainArrow) mainArrow.classList.remove('rotated');
    }

    // Close all subcategories when closing menu
    const subcategoryMenus = document.querySelectorAll('.mobile-subcategory-menu');
    const arrows = document.querySelectorAll('.mobile-subcategory-arrow');

    subcategoryMenus.forEach(menu => menu.classList.remove('active'));
    arrows.forEach(arrow => arrow.classList.remove('rotated'));
}

function toggleMobileSubcategory(categorySlug) {
    const submenu = document.getElementById('submenu-' + categorySlug);
    const arrow = document.getElementById('arrow-' + categorySlug);

    if (!submenu || !arrow) return;

    // Close other open submenus
    const allSubmenus = document.querySelectorAll('.mobile-subcategory-menu');
    const allArrows = document.querySelectorAll('.mobile-subcategory-arrow');

    allSubmenus.forEach(menu => {
        if (menu.id !== 'submenu-' + categorySlug) {
            menu.classList.remove('active');
        }
    });

    allArrows.forEach(arr => {
        if (arr.id !== 'arrow-' + categorySlug) {
            arr.classList.remove('rotated');
        }
    });

    // Toggle current submenu
    submenu.classList.toggle('active');
    arrow.classList.toggle('rotated');
}

function toggleMobileCategory() {
    const submenu = document.getElementById('mobileCategorySubmenu');
    const arrow = document.querySelector('.mobile-category-section .mobile-category-arrow');

    if (submenu && arrow) {
        if (submenu.classList.contains('active')) {
            submenu.classList.remove('active');
            arrow.classList.remove('rotated');
        } else {
            submenu.classList.add('active');
            arrow.classList.add('rotated');
        }
    }
}

// Close mobile menu when clicking outside
document.addEventListener('click', function(event) {
    const menu = document.getElementById('mobileNavMenu');
    const menuBtn = document.querySelector('.mobile-menu-btn');

    if (menu && menu.classList.contains('active') &&
        !menu.contains(event.target) &&
        !menuBtn.contains(event.target)) {
        closeMobileMenu();
    }
});

// Close mobile menu on window resize if screen becomes larger
window.addEventListener('resize', function() {
    if (window.innerWidth > 768) {
        closeMobileMenu();
    }
});

// Prevent menu close when clicking inside menu content
document.getElementById('mobileNavMenu')?.addEventListener('click', function(event) {
    event.stopPropagation();
});
</script>

<script>
    let searchTimeout;

    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchInput');
        const searchSuggestions = document.getElementById('searchSuggestions');
        const inputSuggestion = document.getElementById('input-suggestion');

        if (searchInput && searchSuggestions) {
            // Hiển thị lịch sử khi focus
            searchInput.addEventListener('focus', function() {
                if (this.value.length < 2) {
                    loadSearchHistory();
                    searchSuggestions.style.display = 'block';
                    inputSuggestion.style.display = 'none';
                }
            });

            // Tìm kiếm khi nhập
            searchInput.addEventListener('input', function() {
                const keyword = this.value.trim();

                clearTimeout(searchTimeout);

                if (keyword.length < 2) {
                    loadSearchHistory();
                    searchSuggestions.style.display = 'block';
                    inputSuggestion.style.display = 'none';
                    return;
                }

                searchTimeout = setTimeout(() => {
                    loadSearchSuggestions(keyword);
                }, 300);
            });

            // Ẩn khi click ra ngoài
            document.addEventListener('mousedown', function(e) {
                if (!searchSuggestions.contains(e.target) &&
                    !inputSuggestion.contains(e.target) &&
                    !searchInput.contains(e.target)) {
                    searchSuggestions.style.display = 'none';
                    inputSuggestion.style.display = 'none';
                }
            });
        }
    });

    // Load lịch sử tìm kiếm
    function loadSearchHistory() {
        fetch('/api/search/suggestions')
            .then(response => response.json())
            .then(data => {
                const historySeparator = document.getElementById('historySeparator')
                const historyList = document.getElementById('searchHistoryList');
                const popularTags = document.getElementById('popularSearchTags');
                const historySection = document.getElementById('historySection');

                // Hiển thị lịch sử tìm kiếm
                if (data.search_history && data.search_history.length > 0) {
                    historySeparator.style.display = 'block';
                    historySection.style.display = 'flex';
                    historyList.innerHTML = data.search_history.map(item => `
                    <div class="suggestions flex gap-2 py-[9px] cursor-pointer" onclick="searchKeyword('${item}')">
                        <span class="size-[20px] aspect-square">
                            <img alt="recent_search_${item}" class="w-full h-full" src="/images/recent.svg" />
                        </span>
                        <p class="text-[15px] font-be-vietnam-pro font-[400] flex-1 overflow-hidden">${item}</p>
                    </div>
                `).join('');
                } else {
                    historySeparator.style.display = 'none';
                    historySection.style.display = 'none';
                    historyList.innerHTML = '';
                }

                // Hiển thị tìm kiếm phổ biến
                if (data.popular_searches) {
                    popularTags.innerHTML = data.popular_searches.map(item => `
                    <div class="suggestion-tag" onclick="searchKeyword('${item}')">${item}</div>
                `).join('');
                }
            })
            .catch(error => console.error('Error loading search history:', error));
    }

    // Load gợi ý tìm kiếm
    function loadSearchSuggestions(keyword) {
        //console.log('Loading suggestions for:', keyword);

        fetch(`/api/search/suggestions?q=${encodeURIComponent(keyword)}`)
            .then(response => {
                //console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                //console.log('Search suggestions data:', data);

                const categorySuggestions = document.getElementById('categorySuggestions');
                const productSuggestions = document.getElementById('productSuggestions');
                const searchSuggestions = document.getElementById('searchSuggestions');
                const inputSuggestion = document.getElementById('input-suggestion');
                const suggestionDivider = document.getElementById('suggestionDivider');

                const hasCategories = data.categories && data.categories.length > 0;
                const hasProducts = data.products && data.products.length > 0;

                // Hiển thị gợi ý categories
                if (hasCategories) {
                    categorySuggestions.innerHTML = data.categories.map(category => `
                    <div class="category-item-suggestion" onclick="searchCategory('${category.slug}')">
                        <div class="category-icon green">
                            <img style="border: #dee2e6 solid 1px;border-radius: 16px;" src="storage/${category.logo_path}" alt="Category Icon">
                        </div>
                        <span class="category-text">${category.name}</span>
                    </div>
                `).join('');
                } else {
                    categorySuggestions.innerHTML = '';
                }

                // Hiển thị gợi ý products
                if (hasProducts) {
                    productSuggestions.innerHTML = data.products.map(product => `
                    <div class="product-item" onclick="searchKeyword('${product.name.replace(/'/g, '\\\'').replace(/"/g, '&quot;')}')">
                        <div class="product-image">
                            <img src="${product.image}" alt="${product.name}">
                        </div>
                        <span class="product-text">${product.name}</span>
                    </div>
                `).join('');
                } else {
                    productSuggestions.innerHTML = '';
                }

                // Hiển thị divider chỉ khi có cả categories và products
                if (hasCategories && hasProducts) {
                    suggestionDivider.style.display = 'block';
                } else {
                    suggestionDivider.style.display = 'none';
                }

                // Hiển thị dropdown gợi ý
                if (hasCategories || hasProducts) {
                    searchSuggestions.style.display = 'none';
                    inputSuggestion.style.display = 'block';
                } else {
                    searchSuggestions.style.display = 'block';
                    inputSuggestion.style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error loading suggestions:', error);
                // Fallback: hiển thị lịch sử tìm kiếm
                const searchSuggestions = document.getElementById('searchSuggestions');
                const inputSuggestion = document.getElementById('input-suggestion');
                searchSuggestions.style.display = 'block';
                inputSuggestion.style.display = 'none';
            });
    }

    // Tìm kiếm theo từ khóa
    function searchKeyword(keyword) {
        window.location.href = `/product?search=${encodeURIComponent(keyword)}`;
    }

    // Tìm kiếm theo category
    function searchCategory(categorySlug) {
        window.location.href = `/product?category=${categorySlug}`;
    }

    // Xóa lịch sử tìm kiếm
    function clearSearchHistory() {
        fetch('/api/search/clear-history', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadSearchHistory(); // Reload để ẩn lịch sử
                }
            })
            .catch(error => console.error('Error clearing history:', error));
    }

    function setupMegaMenu() {
        const menuItems = document.querySelectorAll('.mega-menu-item');

        menuItems.forEach(item => {
            // Hover event để hiển thị submenu
            item.addEventListener('mouseenter', function() {
                // Luôn bỏ active ở tất cả items trước
                menuItems.forEach(i => i.classList.remove('active'));

                // Nếu item này có onclick (có category_url), chỉ active nó và không hiện submenu
                if (this.hasAttribute('onclick')) {
                    this.classList.add('active');
                    // Ẩn tất cả content
                    const contentSections = document.querySelectorAll('.mega-menu-content');
                    contentSections.forEach(section => section.classList.remove('active'));
                } else {
                    // Xử lý bình thường cho item không có URL
                    this.classList.add('active');
                    // Lấy id content
                    const categoryId = this.getAttribute('data-category');
                    // Ẩn tất cả content
                    const contentSections = document.querySelectorAll('.mega-menu-content');
                    contentSections.forEach(section => section.classList.remove('active'));
                    // Hiện content tương ứng
                    const activeContent = document.getElementById(categoryId);
                    if (activeContent) {
                        activeContent.classList.add('active');
                    }
                }
            });

            // Click event để redirect (nếu có category_url)
            item.addEventListener('click', function(e) {
                if (this.hasAttribute('onclick')) {
                    e.stopPropagation(); // Ngăn event bubbling
                }
            });
        });
    }

    function showAllCategories(categoryId, categorySlug) {
        const container = document.getElementById(categorySlug);
        if (!container) return;

        const hiddenItems = container.querySelectorAll('.hidden-category');
        hiddenItems.forEach(item => item.classList.remove('hidden-category'));

        const showAllBtn = container.querySelector('.show-all-btn-' + categoryId);
        if (showAllBtn) showAllBtn.style.display = 'none';
    }

    // Khởi tạo và xử lý show/hide mega menu
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(function() {
            setupMegaMenu();

            // Đóng menu khi click ra ngoài
            document.addEventListener('click', function(event) {
                const dropdown = document.querySelector('.dropdown-menu.mega-menu');
                const dropdownToggle = document.querySelector('.dropdown-toggle');
                if (dropdown && dropdownToggle && !dropdown.contains(event.target) && !dropdownToggle.contains(event.target)) {
                    dropdown.classList.remove('active');
                    dropdown.style.display = 'none';
                    // Reset icon về chevron-down
                    const chevronIcon = dropdownToggle.querySelector('.fa-chevron-up');
                    if (chevronIcon) {
                        chevronIcon.classList.remove('fa-chevron-up');
                        chevronIcon.classList.add('fa-chevron-down');
                    }
                }
            });

            // Đóng menu khi touch ra ngoài (cho mobile/tablet)
            document.addEventListener('touchstart', function(event) {
                const dropdown = document.querySelector('.dropdown-menu.mega-menu');
                const dropdownToggle = document.querySelector('.dropdown-toggle');
                if (dropdown && dropdownToggle && !dropdown.contains(event.target) && !dropdownToggle.contains(event.target)) {
                    dropdown.classList.remove('active');
                    dropdown.style.display = 'none';
                    // Reset icon về chevron-down
                    const chevronIcon = dropdownToggle.querySelector('.fa-chevron-up');
                    if (chevronIcon) {
                        chevronIcon.classList.remove('fa-chevron-up');
                        chevronIcon.classList.add('fa-chevron-down');
                    }
                }
            });

            // Hiện menu khi hover/click vào toggle
            const dropdownToggle = document.querySelector('.dropdown-toggle');
            const dropdown = document.querySelector('.dropdown-menu.mega-menu');
            if (dropdownToggle && dropdown) {
                // Kiểm tra device có touch không (mobile/tablet)
                const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

                    // Mobile/Tablet: Sử dụng click event
                    dropdownToggle.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        if (dropdown.style.display === 'block') {
                            dropdown.classList.remove('active');
                            dropdown.style.display = 'none';
                            // Đổi icon về chevron-down
                            const chevronIcon = dropdownToggle.querySelector('.fa-chevron-up');
                            if (chevronIcon) {
                                chevronIcon.classList.remove('fa-chevron-up');
                                chevronIcon.classList.add('fa-chevron-down');
                            }
                        } else {
                            dropdown.classList.add('active');
                            dropdown.style.display = 'block';
                            // Đổi icon thành chevron-up
                            const chevronIcon = dropdownToggle.querySelector('.fa-chevron-down');
                            if (chevronIcon) {
                                chevronIcon.classList.remove('fa-chevron-down');
                                chevronIcon.classList.add('fa-chevron-up');
                            }
                        }
                    });
            }

            // Đóng megamenu khi scroll trang web trên mobile (max-width 768px)
            let scrollTimeout;
            window.addEventListener('scroll', function() {
                const dropdown = document.querySelector('.dropdown-menu.mega-menu');
                if (dropdown && dropdown.style.display === 'block') {
                    // Kiểm tra nếu là touch device (mobile) và screen width <= 768px
                    const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
                    const isMobile = window.innerWidth <= 768;

                    if (isTouchDevice && isMobile) {
                        clearTimeout(scrollTimeout);
                        scrollTimeout = setTimeout(function() {
                            dropdown.classList.remove('active');
                            dropdown.style.display = 'none';
                            // Reset icon về chevron-down
                            const dropdownToggle = document.querySelector('.dropdown-toggle');
                            const chevronIcon = dropdownToggle.querySelector('.fa-chevron-up');
                            if (chevronIcon) {
                                chevronIcon.classList.remove('fa-chevron-up');
                                chevronIcon.classList.add('fa-chevron-down');
                            }
                        }, 5); // Delay ngắn để tránh đóng quá nhanh
                    }
                }
            });
        }, 100);

        // Initialize navigation scroll functionality
        initNavigationScroll();
    });

    // Navigation scroll functionality
    let checkScrollButtons; // Global reference

    function initNavigationScroll() {
        const navMenu = document.getElementById('navigation-menu');
        const leftBtn = document.querySelector('.nav-scroll-left');
        const rightBtn = document.querySelector('.nav-scroll-right');

        if (!navMenu || !leftBtn || !rightBtn) {
            return;
        }

        checkScrollButtons = function() {
            const scrollLeft = navMenu.scrollLeft;
            const scrollWidth = navMenu.scrollWidth;
            const clientWidth = navMenu.clientWidth;



            // Show/hide buttons based on scroll position and content overflow
            const hasOverflow = scrollWidth > clientWidth + 10; // Add small buffer

            if (hasOverflow) {
                leftBtn.style.display = scrollLeft > 5 ? 'flex' : 'none';
                rightBtn.style.display = scrollLeft < (scrollWidth - clientWidth - 10) ? 'flex' : 'none';
            } else {
                leftBtn.style.display = 'none';
                rightBtn.style.display = 'none';
            }
        };

        // Check on load and resize with delay
        setTimeout(checkScrollButtons, 100);
        window.addEventListener('resize', () => setTimeout(checkScrollButtons, 100));
        navMenu.addEventListener('scroll', checkScrollButtons);
    }

    function scrollNavigation(direction) {
        const navMenu = document.getElementById('navigation-menu');
        const leftBtn = document.querySelector('.nav-scroll-left');
        const rightBtn = document.querySelector('.nav-scroll-right');

        if (!navMenu || !leftBtn || !rightBtn) {
            return;
        }

        // TẮT HOÀN TOÀN scroll listener để tránh conflict
        navMenu.removeEventListener('scroll', checkScrollButtons);

        if (direction === 'left') {
            // Ẩn cả 2 nút ngay lập tức
            leftBtn.style.display = 'none';
            rightBtn.style.display = 'none';

            // Scroll về đầu
            navMenu.scrollTo({ left: 0, behavior: 'smooth' });

            // Sử dụng requestAnimationFrame để mượt hơn
            let lastScrollLeft = navMenu.scrollLeft;
            let frameCount = 0;

            function checkScrollComplete() {
                const currentScrollLeft = navMenu.scrollLeft;

                if (Math.abs(currentScrollLeft - lastScrollLeft) < 0.5) {
                    frameCount++;
                    // Đợi 3 frame liên tiếp không thay đổi để chắc chắn scroll đã dừng
                    if (frameCount >= 3) {
                        const scrollWidth = navMenu.scrollWidth;
                        const clientWidth = navMenu.clientWidth;
                        if (scrollWidth > clientWidth + 10) {
                            rightBtn.style.display = 'flex';
                        }

                        // Bật lại scroll listener
                        navMenu.addEventListener('scroll', checkScrollButtons);
                        return; // Dừng recursion
                    }
                } else {
                    frameCount = 0; // Reset nếu vẫn đang scroll
                    lastScrollLeft = currentScrollLeft;
                }

                requestAnimationFrame(checkScrollComplete);
            }

            requestAnimationFrame(checkScrollComplete);

        } else {
            // Ẩn cả 2 nút ngay lập tức
            leftBtn.style.display = 'none';
            rightBtn.style.display = 'none';

            // Tính toán maxScroll và scroll về cuối với buffer lớn
            const scrollWidth = navMenu.scrollWidth;
            const clientWidth = navMenu.clientWidth;
            const maxScroll = scrollWidth - clientWidth;

            navMenu.scrollTo({ left: maxScroll + 300, behavior: 'smooth' }); // Giảm buffer từ 500 xuống 300

            // Sử dụng requestAnimationFrame để mượt hơn
            let lastScrollLeft = navMenu.scrollLeft;
            let frameCount = 0;

            function checkScrollComplete() {
                const currentScrollLeft = navMenu.scrollLeft;

                if (Math.abs(currentScrollLeft - lastScrollLeft) < 0.5) {
                    frameCount++;
                    // Đợi 3 frame liên tiếp không thay đổi để chắc chắn scroll đã dừng
                    if (frameCount >= 3) {
                        const finalScrollWidth = navMenu.scrollWidth;
                        const finalClientWidth = navMenu.clientWidth;

                        if (finalScrollWidth > finalClientWidth + 10) {
                            leftBtn.style.display = 'flex';
                        }

                        // Bật lại scroll listener
                        navMenu.addEventListener('scroll', checkScrollButtons);
                        return; // Dừng recursion
                    }
                } else {
                    frameCount = 0; // Reset nếu vẫn đang scroll
                    lastScrollLeft = currentScrollLeft;
                }

                requestAnimationFrame(checkScrollComplete);
            }

            requestAnimationFrame(checkScrollComplete);
        }
    }
</script>