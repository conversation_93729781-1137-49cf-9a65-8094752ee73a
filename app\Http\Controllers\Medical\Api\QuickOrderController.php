<?php

namespace App\Http\Controllers\Medical\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Webkul\Product\Models\Product;

class QuickOrderController extends Controller
{
    /**
     * Tìm kiếm sản phẩm cho quick order
     */
    public function search(Request $request)
    {
        $keyword = $request->query('q', '');

        // Tìm kiếm sản phẩm với thông tin đầy đủ cho quick order
        $products = Product::where(function($query) {
                $query->where('type', 'configurable')
                      ->orWhere(function($q) {
                          $q->where('type', 'simple')
                            ->whereNull('parent_id');
                      });
            });

        // Nếu có keyword thì thêm điều kiện tìm kiếm
        if (strlen($keyword) > 0) {
            $products = $products->where(function($q) use ($keyword) {
                $q->whereHas('product_flats', function($flat) use ($keyword) {
                    $flat->where('name', 'LIKE', '%' . $keyword . '%')
                         ->orWhere('short_description', 'LIKE', '%' . $keyword . '%');
                })
                ->orWhere('sku', 'LIKE', '%' . $keyword . '%')
                ->orWhereHas('attribute_values', function($attr) use ($keyword) {
                    $attr->whereHas('attribute', function($attribute) {
                        $attribute->where('code', 'active_ingredients');
                    })->where('text_value', 'LIKE', '%' . $keyword . '%');
                });
            });
        }

        $products = $products->with(['images', 'product_flats', 'attribute_values.attribute'])
            ->limit(20)
            ->get();

        // Xử lý dữ liệu sản phẩm
        $processedProducts = $products->map(function($product) {
                try {
                    $image = $product->images->first();
                    $productFlat = $product->product_flats->first();

                    // Lấy thông tin giá và visible_price (giống logic Product model accessor)
                    $price = 0;
                    $visiblePrice = false; // Mặc định là false (báo giá)
                    $priceText = 'Liên hệ';

                    // Sử dụng accessor của Product model để lấy visible_price
                    $productVisiblePrice = $product->visible_price;

                    if ($productVisiblePrice !== null) {
                        $visiblePrice = (bool) $productVisiblePrice;
                    }

                    // Nếu visible_price = true và có giá thì hiển thị giá
                    if ($visiblePrice && $productFlat && $productFlat->price > 0) {
                        $price = $productFlat->price;
                        $priceText = number_format($price, 0, ',', '.') . 'đ';
                    }

                    // Lấy stock thực tế từ bảng product_inventory_indices
                    $inventoryIndex = DB::table('product_inventory_indices')
                        ->where('product_id', $product->id)
                        ->where('channel_id', core()->getCurrentChannel()->id)
                        ->first();

                    $totalStock = $inventoryIndex ? $inventoryIndex->qty : 0;

                    // Lấy SKU từ attribute_values
                    $sku = 'N/A';
                    if ($product->attribute_values) {
                        foreach ($product->attribute_values as $attrValue) {
                            if ($attrValue->attribute && $attrValue->attribute->code === 'sku') {
                                $sku = $attrValue->text_value ?: $product->sku;
                                break;
                            }
                        }
                    }
                    if ($sku === 'N/A') {
                        $sku = $product->sku ?: 'N/A';
                    }

                    return [
                        'id' => $product->id,
                        'name' => $productFlat ? $productFlat->name : ($product->sku ?: 'Unknown Product'),
                        'sku' => $sku,
                        'image' => $image ? asset('storage/' . $image->path) : '/images/product.png',
                        'price' => $price,
                        'price_text' => $priceText,
                        'visible_price' => $visiblePrice,
                        'stock' => $totalStock,
                        'stock_status' => $totalStock > 0 ? 'in-stock' : 'out-of-stock',
                        'stock_text' => $totalStock > 0 ? "Còn hàng" : "Hết hàng",
                        'stock_quantity' => $totalStock . ' sản phẩm',
                        'type' => 'product'
                    ];
                } catch (\Exception $e) {
                    // Log lỗi và trả về null để filter ra
                    Log::error('Error processing product in QuickOrderController: ' . $e->getMessage(), [
                        'product_id' => $product->id ?? 'unknown',
                        'error' => $e->getMessage()
                    ]);
                    return null;
                }
            })
            ->filter() // Loại bỏ các item null
            ->values(); // Reset keys

        return response()->json([
            'products' => $processedProducts,
            'total' => $processedProducts->count(),
            'keyword' => $keyword,
            'message' => $processedProducts->count() > 0
                ? ($keyword ? "Tìm thấy {$processedProducts->count()} sản phẩm cho từ khóa \"{$keyword}\"" : "Hiển thị {$processedProducts->count()} sản phẩm")
                : ($keyword ? "Không tìm thấy sản phẩm nào cho từ khóa \"{$keyword}\"" : "Không có sản phẩm nào")
        ]);
    }
}
