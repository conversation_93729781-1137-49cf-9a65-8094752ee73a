<?php

namespace App\Http\Controllers\Medical;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Webkul\Checkout\Models\Cart;
use Webkul\Product\Models\Product;
use App\Http\Controllers\Medical\PaginationController;

class QuickOrderController extends Controller
{
    protected $productController;

    public function __construct(ProductController $productController)
    {
        $this->productController = $productController;
    }

    public function index(Request $request)
    {
        // Lấy dữ liệu giỏ hàng và báo giá
        $cartItems = collect();
        $quoteItems = collect();
        $cartQuantities = [];
        $quoteQuantities = [];
        $customer = Auth::guard('customer')->user();

        if ($customer) {
            // Debug: Log customer info
            \Log::info('QuickOrder - Customer ID: ' . $customer->id);

            // Lấy Cart items
            $cart = Cart::where('customer_id', $customer->id)
                       ->where('is_active', 1)
                       ->first();

            \Log::info('QuickOrder - Cart found: ' . ($cart ? 'Yes (ID: ' . $cart->id . ')' : 'No'));

            if ($cart) {
                $cartItems = $cart->items()->with(['product', 'product.images'])->get();
                \Log::info('QuickOrder - Cart items count: ' . $cartItems->count());

                // Tạo mapping cho Cart quantities
                foreach ($cartItems as $item) {
                    $cartQuantities[$item->product_id] = $item->quantity;
                    \Log::info('QuickOrder - Cart item: product_id=' . $item->product_id . ', quantity=' . $item->quantity);
                }
            }

            // Lấy Quote items - DÙNG CÙNG LOGIC NHU QUOTECONTROLLER
            $quote = \App\Quote::where('customer_id', $customer->id)
                              ->orderBy('created_at', 'desc')  // Lấy quote MỚI NHẤT (giống QuoteController)
                              ->first();

            \Log::info('QuickOrder - Quote found: ' . ($quote ? 'Yes (ID: ' . $quote->id . ', status: ' . $quote->status . ')' : 'No'));

            if ($quote) {
                $quoteItems = $quote->items()->with(['product', 'product.images'])->get();

                // Debug: Log chi tiết Quote items
                \Log::info('QuickOrder - Quote ID: ' . $quote->id);
                \Log::info('QuickOrder - Quote items count: ' . $quoteItems->count());

                // Tạo mapping cho Quote quantities
                foreach ($quoteItems as $item) {
                    $quoteQuantities[$item->product_id] = $item->quantity;

                    // Debug: Log từng item
                    \Log::info('QuickOrder - Quote item: product_id=' . $item->product_id . ', quantity=' . $item->quantity . ', product_name=' . ($item->product->name ?? 'N/A'));
                }
            }
        }

        // Lấy TẤT CẢ SẢN PHẨM trong database
        $query = Product::query()->with(['images', 'attribute_values.attribute']);

        // Nếu có search thì mới thêm điều kiện tìm kiếm
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('attribute_values', function($q) use ($search) {
                $q->whereHas('attribute', function($attr) {
                    $attr->where('code', 'name');
                })->where('text_value', 'like', "%{$search}%");
            });
        }

        // Lấy TẤT CẢ sản phẩm - không có điều kiện lọc nào khác
        $products = $query->get();

        // Xử lý dữ liệu sản phẩm để hiển thị
        $processedProducts = collect();
        if ($products->isNotEmpty()) {
            try {
                $processedProducts = $this->productController->processProductData($products);
            } catch (\Exception $e) {
                // Nếu processProductData lỗi, sử dụng dữ liệu gốc
                $processedProducts = $products;
            }
        }

        // Sắp xếp sản phẩm: ưu tiên sản phẩm có hàng trước
        $processedProducts = $processedProducts->sort(function ($a, $b) {
            try {
                // Lấy stock quantity của từng sản phẩm
                $stockA = 0;
                $stockB = 0;

                try {
                    $stockA = $a->totalQuantity() ?? 0;
                } catch (\Exception $e) {
                    $stockA = 0;
                }

                try {
                    $stockB = $b->totalQuantity() ?? 0;
                } catch (\Exception $e) {
                    $stockB = 0;
                }

                // Sắp xếp: sản phẩm có hàng (stock > 0) trước, sau đó theo stock giảm dần
                if ($stockA > 0 && $stockB <= 0) {
                    return -1; // A có hàng, B hết hàng -> A trước
                } elseif ($stockA <= 0 && $stockB > 0) {
                    return 1;  // A hết hàng, B có hàng -> B trước
                } else {
                    // Cả hai cùng có hàng hoặc cùng hết hàng -> sắp xếp theo stock giảm dần
                    return $stockB <=> $stockA;
                }
            } catch (\Exception $e) {
                return 0; // Nếu có lỗi, giữ nguyên thứ tự
            }
        })->values(); // Reset keys sau khi sort

        $paginated = PaginationController::paginateCollection($processedProducts, 10);

        // Debug: Log số lượng sản phẩm
        \Log::info('QuickOrder - Total products in database: ' . Product::count());
        \Log::info('QuickOrder - Products after query: ' . $products->count());
        \Log::info('QuickOrder - Products after processing: ' . $processedProducts->count());
        \Log::info('QuickOrder - Search term: ' . ($request->search ?? 'none'));

        return view('medical::quick_order.quick_order', [
            'cartItems' => $cartItems,
            'quoteItems' => $quoteItems,
            'cartQuantities' => $cartQuantities,
            'quoteQuantities' => $quoteQuantities,
            'products' => $paginated
        ]);
    }
}
