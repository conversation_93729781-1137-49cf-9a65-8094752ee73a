/* Container và layout chính */
.quick-order-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    font-family: 'Be Vietnam Pro', sans-serif;
}

/* Header */
.quick-order-header {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.quick-order-search-section {
    margin-bottom: 12px;
}

.quick-order-search-form {
    max-width: 600px;
}

.quick-order-search-bar {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 1rem;
    z-index: 1;
}

.quick-order-search-input {
    flex: 1;
    padding: 12px 90px 12px 45px; /* Tăng padding-right đ<PERSON> có chỗ cho clear button */
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.2s;
}

.quick-order-search-input:focus {
    outline: none;
    border-color: #FF6B00;
    box-shadow: 0 0 0 3px rgba(255, 107, 0, 0.1);
}

.quick-order-search-btn {
    background: #FF6B00;
    color: #fff;
    border: 1px solid #FF6B00;
    border-radius: 0 8px 8px 0;
    padding: 12px 16px;
    font-size: 1rem;
    cursor: pointer;
    transition: background 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quick-order-search-btn:hover {
    background: #e65c00;
    border-color: #e65c00;
}

/* Clear search button */
.quick-order-clear-btn {
    position: absolute;
    right: 60px; /* Để chỗ cho search button */
    top: 50%;
    transform: translateY(-50%);
    background: #dc3545;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 6px 8px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 32px;
    height: 32px;
    z-index: 2;
}

.quick-order-clear-btn:hover {
    background: #c82333;
    transform: translateY(-50%) scale(1.05);
}

.quick-order-clear-btn i {
    font-size: 11px;
}

.search-note {
    color: #e74c3c;
    font-size: 0.875rem;
    margin: 0;
    font-style: italic;
}

/* Main content layout - horizontal */
.quick-order-main {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

/* Left side - Table */
.quick-order-left {
    flex: 1;
}

/* Right side - Summary */
.quick-order-right {
    width: 300px;
    flex-shrink: 0;
}


/* Table layout */
.quick-order-table {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    width: 100%;
}

.quick-order-table-header {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 16px;
    padding: 16px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
    color: #495057;
    align-items: center;
}

.quick-order-checkbox-cell {
    display: flex;
    align-items: center;
    gap: 12px;
}

.quick-order-checkbox {
    width: 18px !important;
    height: 18px !important;
    min-width: 18px !important;
    min-height: 18px !important;
    max-width: 18px !important;
    max-height: 18px !important;
    cursor: pointer;
    flex-shrink: 0;
    box-sizing: border-box;
}

.quick-order-price-cell,
.quick-order-quantity-cell,
.quick-order-total-cell {
    text-align: center;
}

/* Items */
.quick-order-items {
    /* Bỏ giới hạn chiều cao và scroll */
}

.quick-order-item {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 16px;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    align-items: center;
    transition: background-color 0.2s;
}

.quick-order-item:hover {
    background-color: #f8f9fa;
}

/* Sản phẩm có hàng - nổi bật */
.quick-order-item.in-stock {
    border-left: 4px solid #28a745;
    background: #f8fff9;
}

.quick-order-item.in-stock:hover {
    background-color: #e8f5e8;
}

/* Sản phẩm hết hàng - làm mờ */
.quick-order-item.out-of-stock {
    opacity: 0.6;
    border-left: 4px solid #dc3545;
    background: #fff5f5;
}

.quick-order-item.out-of-stock:hover {
    background-color: #ffeaea;
}

.quick-order-item.out-of-stock .quick-order-product-name {
    color: #6c757d;
}

.quick-order-item.out-of-stock .product-price {
    color: #6c757d;
}

/* Sản phẩm báo giá - màu cam */
.quick-order-item.quote-product {
    border-left: 4px solid #FF6B00;
    background: #fff8f0;
}

.quick-order-item.quote-product:hover {
    background-color: #ffebcc;
}

.quick-order-item.quote-product .product-price {
    color: #FF6B00;
    font-weight: 700;
}

.quick-order-product-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.quick-order-product-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.quick-order-product-details {
    flex: 1;
}

.quick-order-product-name {
    font-weight: 600;
    font-size: 1rem;
    color: #333;
    margin-bottom: 4px;
    line-height: 1.3;
}

.quick-order-product-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.875rem;
    color: #666;
}

.product-brand {
    color: #666;
}

.discount-badge {
    background-color: #dc3545;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: bold;
}

.stock-info {
    font-size: 0.8rem;
    color: #28a745;
    margin-left: 8px;
}

.stock-info .text-red {
    color: #dc3545;
}

.stock-info i {
    margin-right: 4px;
}

/* Sort info */
.sort-info {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 6px;
    padding: 10px 12px;
    margin-bottom: 16px;
    font-size: 0.9rem;
    color: #1976d2;
    display: flex;
    align-items: center;
    gap: 8px;
}

.sort-info i {
    color: #1976d2;
}





/* Stock badges */
.in-stock-badge {
    background: #28a745;
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 8px;
    text-transform: uppercase;
}

.out-of-stock-badge {
    background: #dc3545;
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 8px;
    text-transform: uppercase;
}

.quote-badge {
    background: #FF6B00;
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 8px;
    text-transform: uppercase;
}
/* Price styling */
.quick-order-price-cell {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
}

.product-price {
    color: #FF6B00;
    font-weight: 600;
    font-size: 1rem;
    line-height: 1.2;
}

.price-old {
    color: #888;
    text-decoration: line-through;
    font-size: 0.875rem;
    line-height: 1.2;
    margin-right: 0;
    margin-bottom: 2px;
}

/* Quantity control */
.quantity-control {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0;
}

.quantity-btn {
    width: 32px;
    height: 32px;
    border: 1px solid #ddd;
    background: #fff;
    color: #333;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-btn:hover:not(:disabled) {
    background: #FF6B00;
    color: #fff;
    border-color: #FF6B00;
}

.quantity-btn:disabled {
    background: #f8f9fa;
    color: #6c757d;
    border-color: #dee2e6;
    cursor: not-allowed;
    opacity: 0.6;
}

.quantity-btn.decrease {
    border-radius: 4px 0 0 4px;
}

.quantity-btn.increase {
    border-radius: 0 4px 4px 0;
}

.quantity-input {
    width: 60px;
    height: 32px;
    text-align: center;
    border: 1px solid #ddd;
    border-left: none;
    border-right: none;
    background: #fff;
    font-size: 1rem;
    outline: none;
}

/* Product total */
.product-total {
    color: #FF6B00;
    font-weight: 600;
    font-size: 1rem;
}



/* Empty state */
.empty-quick-order {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.no-products-message {
    font-size: 1.125rem;
    margin-bottom: 16px;
}

.continue-shopping-btn {
    color: #FF6B00;
    text-decoration: none;
    font-size: 1rem;
}

.continue-shopping-btn:hover {
    text-decoration: underline;
}

/* Summary sidebar */
.quick-order-summary {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    height: fit-content;
    position: sticky;
    top: 20px;
}

.summary-header {
    padding: 20px 20px 0;
    border-bottom: 1px solid #e9ecef;
}

.summary-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
    margin: 0 0 20px 0;
}

.summary-content {
    padding: 20px;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    font-size: 1rem;
}

.summary-label {
    color: #666;
}

.summary-count {
    font-weight: 600;
    color: #333;
}

.summary-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-top: 1px solid #e9ecef;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 20px;
}

.total-label {
    color: #333;
}

.total-value {
    color: #FF6B00;
    font-size: 1.25rem;
}

/* Checkout button */
.checkout-btn {
    width: 100%;
    background: #FF6B00;
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 14px 20px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.checkout-btn:hover:not(:disabled) {
    background: #e65c00;
}

.checkout-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    opacity: 0.6;
}

.checkout-btn.active {
    background: #FF6B00;
    opacity: 1;
}

/* Add all to cart button */
.add-all-cart-btn {
    background: #28a745;
    color: #fff;
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
}

.add-all-cart-btn:hover:not(:disabled) {
    background: #218838;
}

.add-all-cart-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    opacity: 0.6;
}

.add-all-cart-btn.active {
    background: #28a745;
    opacity: 1;
}

/* Responsive */
@media (max-width: 1024px) {
    .quick-order-main {
        flex-direction: column;
        gap: 16px;
    }

    .quick-order-right {
        width: 100%;
    }

    .quick-order-table-header,
    .quick-order-item {
        grid-template-columns: 2fr 1fr 1fr 1fr;
        gap: 8px;
        padding: 16px 12px;
    }

    .quick-order-summary {
        position: static;
    }
}

@media (max-width: 768px) {
    .quick-order-container {
        padding: 12px;
    }

    .quick-order-search-form {
        max-width: 100%;
    }

    .quick-order-search-input {
        padding: 10px 40px 10px 35px;
        font-size: 0.9rem;
    }

    .quick-order-search-btn {
        padding: 10px 12px;
        font-size: 0.9rem;
    }

    .quick-order-table-header,
    .quick-order-item {
        grid-template-columns: 1fr;
        gap: 12px;
        text-align: left;
    }

    .quick-order-checkbox-cell {
        grid-column: 1;
    }

    .quick-order-price-cell,
    .quick-order-quantity-cell,
    .quick-order-total-cell {
        text-align: left;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .quick-order-price-cell::before {
        content: "Đơn giá:";
        font-weight: 600;
    }

    .quick-order-quantity-cell::before {
        content: "Số lượng:";
        font-weight: 600;
    }

    .quick-order-total-cell::before {
        content: "Thành tiền:";
        font-weight: 600;
    }

    .quick-order-table-header {
        display: none;
    }



    .checkout-btn {
        width: 100%;
        justify-content: center;
    }
}